import { createClient } from '@/lib/supabase/client'
import { Client, CreateClientData, UpdateClientData } from '@/lib/types'

export async function getClients() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('clients')
    .select(`
      *,
      assigned_user:users_profiles!assigned_to(id, full_name),
      projects(id, name, status)
    `)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch clients: ${error.message}`)
  }
  
  return data
}

export async function getClient(id: string) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('clients')
    .select(`
      *,
      assigned_user:users_profiles!assigned_to(id, full_name),
      projects(*)
    `)
    .eq('id', id)
    .single()
  
  if (error) {
    throw new Error(`Failed to fetch client: ${error.message}`)
  }
  
  return data
}

export async function createClient(clientData: CreateClientData) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('clients')
    .insert(clientData)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to create client: ${error.message}`)
  }
  
  return data
}

export async function updateClient(id: string, clientData: UpdateClientData) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('clients')
    .update(clientData)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to update client: ${error.message}`)
  }
  
  return data
}

export async function deleteClient(id: string) {
  const supabase = createClient()
  
  const { error } = await supabase
    .from('clients')
    .delete()
    .eq('id', id)
  
  if (error) {
    throw new Error(`Failed to delete client: ${error.message}`)
  }
}

export async function searchClients(query: string) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('clients')
    .select(`
      *,
      assigned_user:users_profiles!assigned_to(id, full_name),
      projects(id, name, status)
    `)
    .or(`name.ilike.%${query}%,email.ilike.%${query}%,company.ilike.%${query}%`)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to search clients: ${error.message}`)
  }
  
  return data
}

export async function getClientsByStatus(status: string) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('clients')
    .select(`
      *,
      assigned_user:users_profiles!assigned_to(id, full_name),
      projects(id, name, status)
    `)
    .eq('status', status)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch clients by status: ${error.message}`)
  }
  
  return data
}
