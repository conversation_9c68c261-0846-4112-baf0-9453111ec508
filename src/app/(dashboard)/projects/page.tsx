import { Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PageHeader } from "@/components/layout/page-header"
import { Plus } from "lucide-react"

export default function ProjectsPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <PageHeader
        title="Projects"
        description="Manage your projects and track progress"
      >
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          New Project
        </Button>
      </PageHeader>
      
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Project List</CardTitle>
            <CardDescription>
              All your projects and their current status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                No projects found. Create your first project to get started.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
