import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { DollarSign, Users, FolderOpen, FileText, TrendingUp, Target } from "lucide-react"

export default function DashboardPage() {
  // Temporary static data for demonstration
  const metrics = [
    {
      title: "Total Clients",
      value: "12",
      icon: Users,
      description: "Active clients",
    },
    {
      title: "Active Projects",
      value: "8",
      icon: FolderOpen,
      description: "In progress",
    },
    {
      title: "Pending Invoices",
      value: "5",
      icon: FileText,
      description: "Awaiting payment",
    },
    {
      title: "Total Revenue",
      value: "$45,231",
      icon: DollarSign,
      description: "All time",
    },
    {
      title: "Monthly Revenue",
      value: "$12,234",
      icon: TrendingUp,
      description: "This month",
    },
    {
      title: "Conversion Rate",
      value: "68%",
      icon: Target,
      description: "Lead to client",
    },
  ]

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
      </div>

      <div className="space-y-4">
        {/* Metrics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
          {metrics.map((metric) => (
            <Card key={metric.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {metric.title}
                </CardTitle>
                <metric.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>
                <p className="text-xs text-muted-foreground">
                  {metric.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts Row */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <Card className="col-span-4">
            <CardHeader>
              <CardTitle>Revenue Overview</CardTitle>
              <CardDescription>
                Monthly revenue for the current year
              </CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <div className="h-[200px] w-full flex items-center justify-center bg-muted rounded">
                <p className="text-muted-foreground">Revenue chart will be displayed here</p>
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-3">
            <CardHeader>
              <CardTitle>Project Status</CardTitle>
              <CardDescription>
                Current distribution of project statuses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] w-full flex items-center justify-center bg-muted rounded">
                <p className="text-muted-foreground">Project status chart will be displayed here</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activities</CardTitle>
            <CardDescription>
              Latest actions and updates across your business
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                Recent activities will be displayed here once you start using the application
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
