import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { getDashboardMetricsServer } from "@/lib/api/dashboard"
import { DollarSign, Users, FolderOpen, FileText, TrendingUp, Target } from "lucide-react"

export async function DashboardMetrics() {
  const metrics = await getDashboardMetricsServer()

  const metricCards = [
    {
      title: "Total Clients",
      value: metrics.totalClients,
      icon: Users,
      description: "Active clients",
    },
    {
      title: "Active Projects",
      value: metrics.activeProjects,
      icon: FolderOpen,
      description: "In progress",
    },
    {
      title: "Pending Invoices",
      value: metrics.pendingInvoices,
      icon: FileText,
      description: "Awaiting payment",
    },
    {
      title: "Total Revenue",
      value: `$${metrics.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      description: "All time",
    },
    {
      title: "Monthly Revenue",
      value: `$${metrics.monthlyRevenue.toLocaleString()}`,
      icon: TrendingUp,
      description: "This month",
    },
    {
      title: "Conversion Rate",
      value: `${metrics.conversionRate}%`,
      icon: Target,
      description: "Lead to client",
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {metricCards.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {metric.title}
            </CardTitle>
            <metric.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <p className="text-xs text-muted-foreground">
              {metric.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
