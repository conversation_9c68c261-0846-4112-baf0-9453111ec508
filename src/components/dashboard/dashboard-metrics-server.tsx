import { getDashboardMetricsServer } from "@/lib/api/dashboard"
import { DashboardMetrics } from "./dashboard-metrics"

export async function DashboardMetricsServer() {
  try {
    const metrics = await getDashboardMetricsServer()
    return <DashboardMetrics metrics={metrics} />
  } catch (error) {
    console.error("Failed to fetch dashboard metrics:", error)
    // Return fallback data
    const fallbackMetrics = {
      totalClients: 0,
      activeProjects: 0,
      pendingInvoices: 0,
      totalRevenue: 0,
      monthlyRevenue: 0,
      conversionRate: 0,
    }
    return <DashboardMetrics metrics={fallbackMetrics} />
  }
}
