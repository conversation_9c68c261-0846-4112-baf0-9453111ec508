"use client"

import { useEffect, useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { getRecentActivities } from "@/lib/api/dashboard"
import { formatDistanceToNow } from "date-fns"

interface Activity {
  id: string
  action: string
  entity_type: string
  entity_id: string | null
  details: any
  created_at: string
  user: {
    id: string
    full_name: string
  }
}

export function RecentActivities() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchActivities() {
      try {
        const data = await getRecentActivities()
        setActivities(data)
      } catch (error) {
        console.error("Failed to fetch recent activities:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchActivities()
  }, [])

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <div className="h-8 w-8 rounded-full bg-muted animate-pulse" />
            <div className="space-y-2 flex-1">
              <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
              <div className="h-3 bg-muted rounded animate-pulse w-1/2" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (activities.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No recent activities</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {activities.map((activity) => (
        <div key={activity.id} className="flex items-center space-x-4">
          <Avatar className="h-8 w-8">
            <AvatarFallback>
              {activity.user.full_name
                .split(" ")
                .map((n) => n[0])
                .join("")
                .toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 space-y-1">
            <p className="text-sm">
              <span className="font-medium">{activity.user.full_name}</span>{" "}
              {getActivityDescription(activity)}
            </p>
            <p className="text-xs text-muted-foreground">
              {formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })}
            </p>
          </div>
        </div>
      ))}
    </div>
  )
}

function getActivityDescription(activity: Activity): string {
  const { action, entity_type, details } = activity
  
  switch (action) {
    case "created":
      return `created a new ${entity_type.toLowerCase()}`
    case "updated":
      return `updated a ${entity_type.toLowerCase()}`
    case "deleted":
      return `deleted a ${entity_type.toLowerCase()}`
    case "status_changed":
      return `changed ${entity_type.toLowerCase()} status to ${details?.new_status || "unknown"}`
    case "assigned":
      return `was assigned to a ${entity_type.toLowerCase()}`
    case "completed":
      return `completed a ${entity_type.toLowerCase()}`
    case "invoice_sent":
      return `sent an invoice`
    case "payment_received":
      return `received payment for an invoice`
    default:
      return `performed ${action} on ${entity_type.toLowerCase()}`
  }
}
