"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { StatusBadge } from "@/components/ui/status-badge"
import { getClients, searchClients, getClientsByStatus, deleteClient } from "@/lib/api/clients-client"
import { Client, ClientStatus } from "@/lib/types"
import { Search, Plus, MoreHorizontal, Edit, Trash2, Eye, Loader2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ClientForm } from "./client-form"

interface ClientListProps {
  onClientSelect?: (client: Client) => void
}

export function ClientList({ onClientSelect }: ClientListProps) {
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [searchInput, setSearchInput] = useState("")
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [statusFilter, setStatusFilter] = useState<ClientStatus | "all">("all")
  const [showForm, setShowForm] = useState(false)
  const [editingClient, setEditingClient] = useState<Client | undefined>()

  // Debounce search input
  useEffect(() => {
    setIsSearching(true)
    const timeoutId = setTimeout(() => {
      // Only search if input has 3+ characters or is empty
      if (searchInput.length === 0 || searchInput.length >= 3) {
        setDebouncedSearchQuery(searchInput)
      }
      setIsSearching(false)
    }, 500) // 500ms debounce delay

    return () => {
      clearTimeout(timeoutId)
    }
  }, [searchInput])

  const fetchClients = async () => {
    setLoading(true)
    try {
      let data
      if (debouncedSearchQuery) {
        data = await searchClients(debouncedSearchQuery)
      } else if (statusFilter !== "all") {
        data = await getClientsByStatus(statusFilter)
      } else {
        data = await getClients()
      }
      setClients(data)
    } catch (error) {
      console.error("Failed to fetch clients:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchClients()
  }, [debouncedSearchQuery, statusFilter])

  const handleDeleteClient = async (id: string) => {
    if (confirm("Are you sure you want to delete this client?")) {
      try {
        await deleteClient(id)
        fetchClients()
      } catch (error) {
        console.error("Failed to delete client:", error)
      }
    }
  }

  const handleEditClient = (client: Client) => {
    setEditingClient(client)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    fetchClients()
    setEditingClient(undefined)
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-muted rounded w-1/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
                <div className="h-3 bg-muted rounded w-1/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          {isSearching && searchInput.length >= 3 && (
            <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4 animate-spin" />
          )}
          <Input
            placeholder="Search clients by name, email, or company (min 3 characters)..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="pl-10 pr-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={(value: ClientStatus | "all") => setStatusFilter(value)}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="lead">Leads</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="archived">Archived</SelectItem>
          </SelectContent>
        </Select>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Client
        </Button>
      </div>

      {/* Client List */}
      {clients.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              {debouncedSearchQuery || statusFilter !== "all"
                ? "No clients found matching your criteria."
                : "No clients yet. Add your first client to get started."
              }
            </p>
            {searchInput.length > 0 && searchInput.length < 3 && (
              <p className="text-sm text-muted-foreground mt-2">
                Type at least 3 characters to search
              </p>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {clients.map((client) => (
            <Card key={client.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-lg">{client.name}</h3>
                      <StatusBadge status={client.status} />
                    </div>
                    
                    <div className="space-y-1 text-sm text-muted-foreground">
                      {client.email && (
                        <p>📧 {client.email}</p>
                      )}
                      {client.phone && (
                        <p>📞 {client.phone}</p>
                      )}
                      {client.company && (
                        <p>🏢 {client.company}</p>
                      )}
                      {client.lead_source && (
                        <p>📍 Source: {client.lead_source}</p>
                      )}
                    </div>

                    {client.projects && client.projects.length > 0 && (
                      <div className="flex gap-1 flex-wrap">
                        <span className="text-xs text-muted-foreground">Projects:</span>
                        {client.projects.slice(0, 3).map((project) => (
                          <Badge key={project.id} variant="secondary" className="text-xs">
                            {project.name}
                          </Badge>
                        ))}
                        {client.projects.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{client.projects.length - 3} more
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/clients/${client.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditClient(client)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleDeleteClient(client.id)}
                        className="text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Client Form Dialog */}
      <ClientForm
        open={showForm}
        onOpenChange={setShowForm}
        client={editingClient}
        onSuccess={handleFormSuccess}
      />
    </div>
  )
}
